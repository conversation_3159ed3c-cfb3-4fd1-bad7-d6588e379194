/**
 * Course Builder Main Module
 * Handles initialization, mobile navigation, and core functionality
 */

// Global variables
let courseId = '';
let currentSelection = { type: null, id: null };
let autoSaveTimeouts = new Map();
let isDragging = false;

// Initialize the course builder when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Get course ID from meta tag or data attribute
    const courseElement = document.querySelector('[data-course-id]');
    if (courseElement) {
        courseId = courseElement.getAttribute('data-course-id');
    }

    // Initialize the course builder
    initializeCourseBuilder();
    
    // Initialize preview image upload functionality
    initializePreviewImageUpload();
    
    // Initialize course thumbnail upload functionality
    initializeCourseThumbnailUpload();
});

function initializeCourseBuilder() {
    // Initialize mobile navigation
    initializeMobileNavigation();

    // Initialize drag and drop
    initializeDragAndDrop();

    // Initialize auto-save
    initializeAutoSave();

    // Initialize event listeners
    initializeEventListeners();

    // Initialize chapter accordion
    initializeChapterAccordion();

    // Show course details by default if no chapters
    if (document.querySelectorAll('.chapter-item').length === 0) {
        selectItem('course', courseId);
    }
}

function initializeMobileNavigation() {
    const mobileToggle = document.getElementById('mobile-sidebar-toggle');
    const mobileClose = document.getElementById('mobile-sidebar-close');
    const sidebar = document.getElementById('curriculum-sidebar');
    const overlay = document.getElementById('mobile-sidebar-overlay');

    // Mobile sidebar toggle
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.remove('-translate-x-full');
            overlay.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        });
    }

    // Mobile sidebar close
    if (mobileClose) {
        mobileClose.addEventListener('click', closeMobileSidebar);
    }

    // Overlay click to close
    if (overlay) {
        overlay.addEventListener('click', closeMobileSidebar);
    }

    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !sidebar.classList.contains('-translate-x-full')) {
            closeMobileSidebar();
        }
    });

    // Auto-close sidebar on desktop resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) { // lg breakpoint
            closeMobileSidebar();
        }
    });

    function closeMobileSidebar() {
        sidebar.classList.add('-translate-x-full');
        overlay.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }
}

function initializeChapterAccordion() {
    const allChapters = document.querySelectorAll('.chapter-item');
    
    // Collapse all chapters initially except the first one
    allChapters.forEach((chapter, index) => {
        const chapterIdAttr = chapter.getAttribute('data-chapter-id');
        const lecturesContainer = document.getElementById(`chapter-lectures-${chapterIdAttr}`);
        const toggleIcon = chapter.querySelector('.chapter-toggle');
        
        if (lecturesContainer && toggleIcon) {
            if (index === 0) {
                // Keep first chapter expanded
                lecturesContainer.classList.remove('collapsed');
                toggleIcon.classList.remove('fa-chevron-right');
                toggleIcon.classList.add('fa-chevron-down');
            } else {
                // Collapse other chapters
                lecturesContainer.classList.add('collapsed');
                toggleIcon.classList.remove('fa-chevron-down');
                toggleIcon.classList.add('fa-chevron-right');
            }
        }
    });
}

function initializeEventListeners() {
    // Add chapter buttons (desktop and mobile)
    document.getElementById('add-chapter-btn')?.addEventListener('click', addChapter);
    document.getElementById('add-chapter-btn-mobile')?.addEventListener('click', function() {
        addChapter();
        // Close mobile/tablet sidebar after adding chapter
        const sidebar = document.getElementById('curriculum-sidebar');
        const overlay = document.getElementById('mobile-sidebar-overlay');
        if (window.innerWidth < 1024) {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    });
    document.getElementById('add-first-chapter-btn')?.addEventListener('click', addChapter);

    // Publish toggle button
    document.getElementById('publish-toggle-btn')?.addEventListener('click', togglePublishStatus);
    
    // Featured toggle button
    const featuredToggleBtn = document.getElementById('featured-toggle-btn');
    if (featuredToggleBtn) {
        featuredToggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            toggleFeaturedStatus();
        });
    }

    // Prevent form submission
    document.getElementById('course-details-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
    });

    // Touch-friendly interactions for mobile
    if ('ontouchstart' in window) {
        // Add touch feedback for interactive elements
        document.querySelectorAll('.chapter-item, .lecture-item').forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.backgroundColor = 'rgba(127, 29, 29, 0.2)';
            });
            item.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.backgroundColor = '';
                }, 150);
            });
        });
    }
}

function selectItem(type, id) {
    if (isDragging) return; // Don't select during drag operations

    currentSelection = { type, id };

    // Update sidebar selection
    updateSidebarSelection(type, id);

    // Load content in main editor
    loadEditor(type, id);

    // Close mobile/tablet sidebar when selecting an item
    if (window.innerWidth < 1024) {
        const sidebar = document.getElementById('curriculum-sidebar');
        const overlay = document.getElementById('mobile-sidebar-overlay');
        if (sidebar && overlay) {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    }
}

function updateSidebarSelection(type, id) {
    // Remove existing selections
    document.querySelectorAll('.chapter-item, .lecture-item').forEach(item => {
        item.classList.remove('bg-red-900', 'border-red-600');
    });

    // Add selection to current item
    if (type === 'chapter') {
        const chapterItem = document.querySelector(`[data-chapter-id="${id}"]`);
        if (chapterItem) {
            chapterItem.classList.add('bg-red-900', 'border-red-600');
        }
    } else if (type === 'lecture') {
        const lectureItem = document.querySelector(`[data-lecture-id="${id}"]`);
        if (lectureItem) {
            lectureItem.classList.add('bg-red-900', 'border-red-600');
        }
    }
}

// Helper functions for DOM manipulation
function toggleChapter(chapterId) {
    const clickedLecturesContainer = document.getElementById(`chapter-lectures-${chapterId}`);
    const clickedToggleIcon = document.querySelector(`[data-chapter-id="${chapterId}"] .chapter-toggle`);
    
    if (!clickedLecturesContainer || !clickedToggleIcon) return;
    
    const isCurrentlyExpanded = !clickedLecturesContainer.classList.contains('collapsed');
    
    // Collapse all chapters first (accordion behavior)
    const allChapters = document.querySelectorAll('.chapter-item');
    allChapters.forEach(chapter => {
        const chapterIdAttr = chapter.getAttribute('data-chapter-id');
        const lecturesContainer = document.getElementById(`chapter-lectures-${chapterIdAttr}`);
        const toggleIcon = chapter.querySelector('.chapter-toggle');
        
        if (lecturesContainer && toggleIcon) {
            lecturesContainer.classList.add('collapsed');
            toggleIcon.classList.remove('fa-chevron-down');
            toggleIcon.classList.add('fa-chevron-right');
        }
    });
    
    // If the clicked chapter wasn't expanded, expand it
    if (!isCurrentlyExpanded) {
        clickedLecturesContainer.classList.remove('collapsed');
        clickedToggleIcon.classList.remove('fa-chevron-right');
        clickedToggleIcon.classList.add('fa-chevron-down');
    }
}

// Course preview content toggle function
function togglePreviewContent() {
    const previewTypeSelects = document.querySelectorAll('#preview-type-select, #preview-type-select-desktop');
    const previewContentLabels = document.querySelectorAll('#preview-content-label, #preview-content-label-desktop');
    const previewContentInputs = document.querySelectorAll('#preview-content-input, #preview-content-input-desktop');
    const previewContentHelps = document.querySelectorAll('#preview-content-help, #preview-content-help-desktop');
    const previewImageUploads = document.querySelectorAll('#preview-image-upload, #preview-image-upload-desktop');
    
    previewTypeSelects.forEach((select, index) => {
        const selectedType = select.value;
        const label = previewContentLabels[index];
        const input = previewContentInputs[index];
        const help = previewContentHelps[index];
        const imageUpload = previewImageUploads[index];
        
        if (label && input && help && imageUpload) {
            switch (selectedType) {
                case 'image':
                    label.textContent = 'Preview Image';
                    input.style.display = 'none';
                    imageUpload.classList.remove('hidden');
                    help.textContent = 'Upload an image file to display as course preview';
                    break;
                case 'video':
                    label.textContent = 'Video URL';
                    input.style.display = 'block';
                    input.placeholder = 'Enter video URL (e.g., https://example.com/video.mp4)';
                    imageUpload.classList.add('hidden');
                    help.textContent = 'Enter the URL of the video file to display as course preview';
                    break;
                case 'youtube':
                    label.textContent = 'YouTube URL';
                    input.style.display = 'block';
                    input.placeholder = 'Enter YouTube URL (e.g., https://www.youtube.com/watch?v=VIDEO_ID)';
                    imageUpload.classList.add('hidden');
                    help.textContent = 'Enter the YouTube video URL to embed as course preview';
                    break;
                default:
                    label.textContent = 'Preview Content';
                    input.style.display = 'block';
                    input.placeholder = 'Enter preview content';
                    imageUpload.classList.add('hidden');
                    help.textContent = 'Enter the URL or path for your course preview';
            }
        }
    });
}

// Preview image upload functions
function initializePreviewImageUpload() {
    // Initialize file input handlers
    const fileInputs = document.querySelectorAll('#preview-image-file, #preview-image-file-desktop');
    fileInputs.forEach(input => {
        input.addEventListener('change', handlePreviewImageSelect);
    });
    
    // Initialize drag and drop
    const uploadAreas = document.querySelectorAll('#preview-image-upload .border-dashed, #preview-image-upload-desktop .border-dashed');
    uploadAreas.forEach(area => {
        area.addEventListener('dragover', handleDragOver);
        area.addEventListener('dragleave', handleDragLeave);
        area.addEventListener('drop', handlePreviewImageDrop);
    });
}

function handlePreviewImageSelect(event) {
    const file = event.target.files[0];
    if (file) {
        previewImageFile(file, event.target.id.includes('desktop'));
    }
}

function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('border-red-500', 'bg-red-900', 'bg-opacity-20');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-red-500', 'bg-red-900', 'bg-opacity-20');
}

function handlePreviewImageDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-red-500', 'bg-red-900', 'bg-opacity-20');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            const isDesktop = event.currentTarget.closest('#preview-image-upload-desktop') !== null;
            previewImageFile(file, isDesktop);
        }
    }
}

function previewImageFile(file, isDesktop = false) {
    // Validate file
    if (!file.type.startsWith('image/')) {
        alert('Please select an image file.');
        return;
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
        alert('File size must be less than 5MB.');
        return;
    }
    
    const suffix = isDesktop ? '-desktop' : '';
    
    // Show preview immediately
    const reader = new FileReader();
    reader.onload = function(e) {
        const previewImg = document.getElementById(`preview-image-preview${suffix}`);
        const displayDiv = document.getElementById(`preview-image-display${suffix}`);
        
        if (previewImg && displayDiv) {
            previewImg.src = e.target.result;
            displayDiv.classList.remove('hidden');
        }
    };
    reader.readAsDataURL(file);
    
    // Upload file to server
    uploadPreviewImage(file, isDesktop);
}

function uploadPreviewImage(file, isDesktop = false) {
    const formData = new FormData();
    formData.append('preview_image', file);
    
    // Show loading state
    const suffix = isDesktop ? '-desktop' : '';
    const uploadArea = document.getElementById(`preview-image-upload${suffix}`);
    if (uploadArea) {
        uploadArea.style.opacity = '0.7';
        uploadArea.style.pointerEvents = 'none';
    }
    
    fetch(`/instructor/course-builder/${courseId}/upload-preview-image`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update preview type select to 'image'
            const previewTypeSelects = document.querySelectorAll('select[name="preview_type"]');
            previewTypeSelects.forEach(select => {
                select.value = 'image';
            });
            
            // Update preview content input with the file path
            const previewContentInputs = document.querySelectorAll('input[name="preview_content"]');
            previewContentInputs.forEach(input => {
                input.value = data.data.preview_content;
            });
            
            console.log('Preview image uploaded successfully:', data.data.preview_url);
        } else {
            alert('Failed to upload image: ' + data.message);
            // Remove preview on error
            if (isDesktop) {
                removePreviewImageDesktop();
            } else {
                removePreviewImage();
            }
        }
    })
    .catch(error => {
        console.error('Error uploading preview image:', error);
        alert('Failed to upload image. Please try again.');
        // Remove preview on error
        if (isDesktop) {
            removePreviewImageDesktop();
        } else {
            removePreviewImage();
        }
    })
    .finally(() => {
        // Remove loading state
        if (uploadArea) {
            uploadArea.style.opacity = '1';
            uploadArea.style.pointerEvents = 'auto';
        }
    });
}

function removePreviewImage() {
    const fileInput = document.getElementById('preview-image-file');
    const displayDiv = document.getElementById('preview-image-display');
    const previewImg = document.getElementById('preview-image-preview');
    
    if (fileInput) fileInput.value = '';
    if (displayDiv) displayDiv.classList.add('hidden');
    if (previewImg) previewImg.src = '';
}

function removePreviewImageDesktop() {
    const fileInput = document.getElementById('preview-image-file-desktop');
    const displayDiv = document.getElementById('preview-image-display-desktop');
    const previewImg = document.getElementById('preview-image-preview-desktop');
    
    if (fileInput) fileInput.value = '';
    if (displayDiv) displayDiv.classList.add('hidden');
    if (previewImg) previewImg.src = '';
}

// Course thumbnail upload functions
function initializeCourseThumbnailUpload() {
    // Initialize file input handlers (desktop and mobile)
    const fileInputs = document.querySelectorAll('#course-thumbnail-file, #course-thumbnail-file-mobile');
    fileInputs.forEach(input => {
        input.addEventListener('change', handleCourseThumbnailSelect);
    });
    
    // Initialize drag and drop for desktop
    const uploadArea = document.querySelector('#course-thumbnail-display')?.previousElementSibling;
    if (uploadArea && uploadArea.classList.contains('border-dashed')) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleCourseThumbnailDrop);
    }
    
    // Initialize drag and drop for mobile
    const mobileUploadArea = document.querySelector('#course-thumbnail-display-mobile')?.previousElementSibling;
    if (mobileUploadArea && mobileUploadArea.classList.contains('border-dashed')) {
        mobileUploadArea.addEventListener('dragover', handleDragOver);
        mobileUploadArea.addEventListener('dragleave', handleDragLeave);
        mobileUploadArea.addEventListener('drop', handleCourseThumbnailDropMobile);
    }
}

function handleCourseThumbnailSelect(event) {
    const file = event.target.files[0];
    if (file) {
        const isMobile = event.target.id.includes('mobile');
        courseThumbnailFile(file, isMobile);
    }
}

function handleCourseThumbnailDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-red-500', 'bg-red-900', 'bg-opacity-20');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            courseThumbnailFile(file, false);
        }
    }
}

function handleCourseThumbnailDropMobile(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('border-red-500', 'bg-red-900', 'bg-opacity-20');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            courseThumbnailFile(file, true);
        }
    }
}

function courseThumbnailFile(file, isMobile = false) {
    // Validate file
    if (!file.type.startsWith('image/')) {
        alert('Please select an image file.');
        return;
    }
    
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
        alert('File size must be less than 5MB.');
        return;
    }
    
    const suffix = isMobile ? '-mobile' : '';
    
    // Show preview immediately
    const reader = new FileReader();
    reader.onload = function(e) {
        const previewImg = document.getElementById(`course-thumbnail-preview${suffix}`);
        const displayDiv = document.getElementById(`course-thumbnail-display${suffix}`);
        
        if (previewImg && displayDiv) {
            previewImg.src = e.target.result;
            displayDiv.classList.remove('hidden');
        }
    };
    reader.readAsDataURL(file);
    
    // Upload file to server
    uploadCourseThumbnail(file, isMobile);
}

function uploadCourseThumbnail(file, isMobile = false) {
    const formData = new FormData();
    formData.append('course_image', file);
    
    const suffix = isMobile ? '-mobile' : '';
    
    // Show loading state
    const uploadArea = document.querySelector(`#course-thumbnail-display${suffix}`)?.previousElementSibling;
    if (uploadArea) {
        uploadArea.style.opacity = '0.7';
        uploadArea.style.pointerEvents = 'none';
    }
    
    fetch(`/instructor/course-builder/${courseId}/upload-course-thumbnail`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Course thumbnail uploaded successfully:', data.data.image_url);
        } else {
            alert('Failed to upload thumbnail: ' + data.message);
            if (isMobile) {
                removeCourseThumbnailMobile();
            } else {
                removeCourseThumbnail();
            }
        }
    })
    .catch(error => {
        console.error('Error uploading course thumbnail:', error);
        alert('Failed to upload thumbnail. Please try again.');
        if (isMobile) {
            removeCourseThumbnailMobile();
        } else {
            removeCourseThumbnail();
        }
    })
    .finally(() => {
        // Remove loading state
        if (uploadArea) {
            uploadArea.style.opacity = '1';
            uploadArea.style.pointerEvents = 'auto';
        }
    });
}

function removeCourseThumbnail() {
    const fileInput = document.getElementById('course-thumbnail-file');
    const displayDiv = document.getElementById('course-thumbnail-display');
    const previewImg = document.getElementById('course-thumbnail-preview');
    
    if (fileInput) fileInput.value = '';
    if (displayDiv) displayDiv.classList.add('hidden');
    if (previewImg) previewImg.src = '';
}

// Mobile course thumbnail functions
function removeCourseThumbnailMobile() {
    const fileInput = document.getElementById('course-thumbnail-file-mobile');
    const displayDiv = document.getElementById('course-thumbnail-display-mobile');
    const previewImg = document.getElementById('course-thumbnail-preview-mobile');
    
    if (fileInput) fileInput.value = '';
    if (displayDiv) displayDiv.classList.add('hidden');
    if (previewImg) previewImg.src = '';
}

// Expose functions to global scope for inline event handlers
// Note: Some functions are defined in other modules and will be available after they load
window.selectItem = selectItem;
window.toggleChapter = toggleChapter;
window.togglePreviewContent = togglePreviewContent;
window.removePreviewImage = removePreviewImage;
window.removePreviewImageDesktop = removePreviewImageDesktop;
window.removeCourseThumbnail = removeCourseThumbnail;
window.removeCourseThumbnailMobile = removeCourseThumbnailMobile;
// addChapter, addLecture, deleteChapter, deleteLecture are defined in course-builder-crud.js
// toggleLectureContent is defined in course-builder-lecture-editor.js
// saveCourse, saveChapter, saveLecture are defined in course-builder-api.js
// togglePublishStatus is defined in course-builder-api.js
