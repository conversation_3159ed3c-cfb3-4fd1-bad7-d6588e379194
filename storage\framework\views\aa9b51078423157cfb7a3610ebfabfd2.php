<?php $__env->startSection('title', 'Instructor Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-8">
                <!-- Mobile-responsive header layout -->
                <div class="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                    <div>
                        <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-white">Instructor <span class="text-red-500">Dashboard</span></h1>
                        <p class="mt-2 text-base md:text-lg text-gray-400">Welcome back, <?php echo e($instructor->name); ?>!</p>
                    </div>

                    <!-- Mobile: Stack buttons vertically, Desktop: Side by side -->
                    <div class="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3">
                        <button onclick="showCreateCourseModal()"
                           class="bg-red-600 hover:bg-red-700 text-white px-4 py-3 md:px-6 md:py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center gap-2 text-sm md:text-base">
                            <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span class="hidden sm:inline">Create New Course</span>
                            <span class="sm:hidden">Create Course</span>
                        </button>
                        <a href="<?php echo e(route('instructor.courses.index')); ?>"
                           class="bg-gray-800 hover:bg-gray-700 text-white px-4 py-3 md:px-6 md:py-3 rounded-lg font-medium transition-colors inline-flex items-center justify-center gap-2 border border-gray-700 text-sm md:text-base">
                            <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <span class="hidden sm:inline">Manage Courses</span>
                            <span class="sm:hidden">Courses</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-red-500 transition-colors">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-600/20 text-red-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Courses</p>
                        <p class="text-2xl font-bold text-white"><?php echo e($stats['total_courses']); ?></p>
                        <p class="text-xs text-green-400"><?php echo e($stats['published_courses']); ?> published</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-red-500 transition-colors">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-600/20 text-green-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Students</p>
                        <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['total_students'])); ?></p>
                        <p class="text-xs text-green-400"><?php echo e($stats['active_students']); ?> active</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-red-500 transition-colors">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-600/20 text-yellow-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Revenue</p>
                        <p class="text-2xl font-bold text-white">$<?php echo e(number_format($revenueStats['total_revenue'], 2)); ?></p>
                        <p class="text-xs text-green-400">$<?php echo e(number_format($revenueStats['this_month_revenue'], 2)); ?> this month</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-red-500 transition-colors">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-600/20 text-purple-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Average Rating</p>
                        <p class="text-2xl font-bold text-white"><?php echo e(number_format($reviewStats['average_rating'], 1)); ?></p>
                        <p class="text-xs text-green-400"><?php echo e($reviewStats['total_reviews']); ?> reviews</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Courses -->
            <div class="bg-gray-800 border border-gray-700 rounded-xl">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-medium text-white">Recent Courses</h3>
                </div>
                <div class="p-6">
                    <?php if($recentCourses->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $recentCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-4 border border-gray-700 rounded-lg hover:border-red-500 transition-colors">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-white"><?php echo e($course->title); ?></h4>
                                        <p class="text-sm text-gray-400"><?php echo e($course->enrollments_count); ?> students • <?php echo e($course->chapters_count); ?> chapters</p>
                                        <div class="flex items-center mt-2">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                                <?php if($course->status === 'published'): ?> bg-green-600/20 text-green-400
                                                <?php elseif($course->status === 'draft'): ?> bg-yellow-600/20 text-yellow-400
                                                <?php else: ?> bg-gray-600/20 text-gray-400 <?php endif; ?>">
                                                <?php echo e(ucfirst($course->status)); ?>

                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <a href="<?php echo e(route('instructor.course-builder.show', $course)); ?>"
                                           class="text-red-500 hover:text-red-400 font-medium">
                                            Edit
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="mt-4 text-center">
                            <a href="<?php echo e(route('instructor.courses.index')); ?>"
                               class="text-red-500 hover:text-red-400 font-medium">
                                View All Courses →
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="w-16 h-16 text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <p class="text-gray-400 mb-4">You haven't created any courses yet.</p>
                            <button onclick="showCreateCourseModal()"
                               class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                Create Your First Course
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Enrollments -->
            <div class="bg-gray-800 border border-gray-700 rounded-xl">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-medium text-white">Recent Enrollments</h3>
                </div>
                <div class="p-6">
                    <?php if($recentEnrollments->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $recentEnrollments->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-3 border border-gray-700 rounded-lg hover:border-red-500 transition-colors">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-red-600/20 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="font-medium text-white"><?php echo e($enrollment->user->name); ?></p>
                                            <p class="text-sm text-gray-400"><?php echo e($enrollment->course->title); ?></p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-400"><?php echo e($enrollment->enrolled_at->diffForHumans()); ?></p>
                                        <div class="w-16 bg-gray-700 rounded-full h-2 mt-1">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: <?php echo e($enrollment->progress_percentage); ?>%"></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="w-16 h-16 text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <p class="text-gray-400">No enrollments yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Course Performance Chart -->
        <?php if($coursePerformance->count() > 0): ?>
        <div class="mt-8 bg-gray-800 border border-gray-700 rounded-xl">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-medium text-white">Course Performance</h3>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-700">
                        <thead class="bg-gray-900">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Course</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Students</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Rating</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Reviews</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Revenue</th>
                            </tr>
                        </thead>
                        <tbody class="bg-gray-800 divide-y divide-gray-700">
                            <?php $__currentLoopData = $coursePerformance; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="hover:bg-gray-700 transition-colors">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-white"><?php echo e($course->title); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php echo e(number_format($course->total_enrollments)); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="text-sm text-white"><?php echo e(number_format($course->average_rating ?: 0, 1)); ?></span>
                                            <div class="ml-2 flex">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <svg class="w-3 h-3 <?php echo e($i <= ($course->average_rating ?: 0) ? 'text-yellow-400' : 'text-gray-600'); ?> fill-current" viewBox="0 0 20 20">
                                                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                    </svg>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?php echo e(number_format($course->total_reviews)); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-400 font-medium">
                                        $<?php echo e(number_format($course->completedPayments->sum('amount'), 2)); ?>

                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Create Course Modal -->
<div id="create-course-modal" class="fixed inset-0 bg-black bg-opacity-75 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border border-gray-700 w-96 shadow-lg rounded-md bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-white">Create New Course</h3>
                <button onclick="hideCreateCourseModal()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="create-course-form" action="<?php echo e(route('instructor.courses.create-and-build')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="mb-4">
                    <label for="course-title" class="block text-sm font-medium text-gray-300 mb-2">Course Title</label>
                    <input type="text" id="course-title" name="title" required
                           class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                           placeholder="Enter your course title">
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideCreateCourseModal()"
                            class="px-4 py-2 bg-gray-700 text-gray-300 text-base font-medium rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                        Create Course
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Auto-refresh quick stats every 5 minutes
    setInterval(function() {
        fetch('<?php echo e(route("instructor.dashboard.quick-stats")); ?>')
            .then(response => response.json())
            .then(data => {
                // Update stats if needed
                console.log('Stats updated:', data);
            })
            .catch(error => console.error('Error updating stats:', error));
    }, 300000); // 5 minutes

    // Create course modal functions
    window.showCreateCourseModal = function() {
        document.getElementById('create-course-modal').classList.remove('hidden');
        document.getElementById('course-title').focus();
    };

    window.hideCreateCourseModal = function() {
        document.getElementById('create-course-modal').classList.add('hidden');
        document.getElementById('course-title').value = '';
    };
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/instructor/dashboard.blade.php ENDPATH**/ ?>