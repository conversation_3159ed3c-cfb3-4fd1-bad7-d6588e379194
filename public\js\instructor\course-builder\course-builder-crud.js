/**
 * Course Builder CRUD Operations Module
 * Handles create, read, update, delete operations for chapters and lectures
 */

// CRUD operations
function addChapter() {
    const formData = new FormData();
    formData.append('title', 'New Chapter');
    formData.append('description', '');

    fetch(`/instructor/course-builder/${courseId}/chapters`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addChapterToSidebar(data.data);
            selectItem('chapter', data.data.id);
            showSuccess('Chapter added successfully');
            
            // Hide welcome state if it's visible
            const welcomeState = document.getElementById('welcome-state');
            if (welcomeState && !welcomeState.classList.contains('hidden')) {
                welcomeState.classList.add('hidden');
            }
        } else {
            showError(data.message || 'Failed to add chapter');
        }
    })
    .catch(error => {
        console.error('Error adding chapter:', error);
        showError('Network error while adding chapter');
    });
}

function addLecture(chapterId) {
    const formData = new FormData();
    formData.append('title', 'New Lecture');
    formData.append('type', 'video');
    formData.append('chapter_id', chapterId);

    fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLectureToSidebar(chapterId, data.data);
            selectItem('lecture', data.data.id);
            updateChapterLectureCount(chapterId);
            showSuccess('Lecture added successfully');
        } else {
            showError(data.message || 'Failed to add lecture');
        }
    })
    .catch(error => {
        console.error('Error adding lecture:', error);
        showError('Network error while adding lecture');
    });
}

function deleteChapter(chapterId) {
    if (!confirm('Are you sure you want to delete this chapter? This will also delete all lectures in this chapter.')) {
        return;
    }

    fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove chapter from sidebar
            const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);
            if (chapterElement) {
                chapterElement.remove();
            }
            
            // Hide editors and show welcome state if no chapters left
            document.getElementById('chapter-editor')?.classList.add('hidden');
            if (document.querySelectorAll('.chapter-item').length === 0) {
                document.getElementById('welcome-state')?.classList.remove('hidden');
            }
            
            showSuccess('Chapter deleted successfully');
        } else {
            showError(data.message || 'Failed to delete chapter');
        }
    })
    .catch(error => {
        console.error('Error deleting chapter:', error);
        showError('Network error while deleting chapter');
    });
}

function deleteLecture(lectureId) {
    if (!confirm('Are you sure you want to delete this lecture?')) {
        return;
    }

    // Find the chapter ID for this lecture
    const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
    const chapterId = lectureElement?.closest('.chapter-lectures')?.id.replace('chapter-lectures-', '');

    fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove lecture from sidebar
            if (lectureElement) {
                lectureElement.remove();
            }
            
            // Update chapter lecture count
            if (chapterId) {
                updateChapterLectureCount(chapterId);
            }
            
            // Hide lecture editor
            document.getElementById('lecture-editor')?.classList.add('hidden');
            
            showSuccess('Lecture deleted successfully');
        } else {
            showError(data.message || 'Failed to delete lecture');
        }
    })
    .catch(error => {
        console.error('Error deleting lecture:', error);
        showError('Network error while deleting lecture');
    });
}

// Helper functions for DOM manipulation
function addChapterToSidebar(chapter) {
    if (!chapter || !chapter.id) {
        console.error('Invalid chapter data:', chapter);
        showError('Invalid chapter data received');
        return;
    }

    const curriculumTree = document.getElementById('curriculum-tree');
    const emptyState = document.getElementById('empty-curriculum');

    if (emptyState) {
        emptyState.remove();
    }

    const chapterHTML = `
        <div class="chapter-item bg-gray-800 rounded-lg border border-gray-700" 
             data-chapter-id="${chapter.id}"
             data-chapter-index="0">
            <!-- Chapter Header -->
            <div class="chapter-header p-4 cursor-pointer hover:bg-gray-750 transition-colors"
                 onclick="selectItem('chapter', '${chapter.id}')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-folder text-yellow-500"></i>
                            <span class="text-white font-medium">${chapter.title}</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-400">0 lectures</span>
                        <div class="flex items-center space-x-1">
                            ${chapter.is_published ? '<i class="fas fa-eye text-green-500 text-xs published-icon" title="Published"></i>' : '<i class="fas fa-eye-slash text-gray-500 text-xs published-icon" title="Unpublished"></i>'}
                        </div>
                        <button class="text-gray-400 hover:text-white transition-colors"
                                onclick="event.stopPropagation(); toggleChapter('${chapter.id}')">
                            <i class="fas fa-chevron-right chapter-toggle"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Chapter Lectures -->
            <div class="chapter-lectures collapsed pl-8 pb-2" id="chapter-lectures-${chapter.id}">
                <!-- Add Lecture Button -->
                <div class="p-3">
                    <button class="add-lecture-btn w-full text-left text-gray-400 hover:text-white transition-colors text-sm"
                            data-chapter-id="${chapter.id}"
                            onclick="addLecture('${chapter.id}')">
                        <i class="fas fa-plus mr-2"></i>Add Lecture
                    </button>
                </div>
            </div>
        </div>
    `;

    curriculumTree.insertAdjacentHTML('beforeend', chapterHTML);
    
    // Re-initialize drag and drop for the new chapter
    initializeDragAndDrop();
}

function addLectureToSidebar(chapterId, lecture) {
    if (!lecture || !lecture.id) {
        console.error('Invalid lecture data:', lecture);
        showError('Invalid lecture data received');
        return;
    }

    const lecturesContainer = document.getElementById(`chapter-lectures-${chapterId}`);
    if (!lecturesContainer) {
        console.error('Lectures container not found for chapter:', chapterId);
        return;
    }

    const addButton = lecturesContainer.querySelector('.add-lecture-btn').parentElement;

    const lectureHTML = `
        <div class="lecture-item p-3 hover:bg-gray-750 rounded cursor-pointer transition-colors"
             data-lecture-id="${lecture.id}"
             data-lecture-index="0"
             onclick="selectItem('lecture', '${lecture.id}')">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                        <i class="fas fa-grip-vertical text-xs"></i>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="${getLectureIcon(lecture.type)}"></i>
                        <span class="text-gray-300 text-sm">${lecture.title}</span>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    ${lecture.duration_minutes ? `<span class="text-xs text-gray-500">${lecture.duration_minutes}min</span>` : ''}
                    <div class="flex items-center space-x-1">
                        ${lecture.is_published ? '<i class="fas fa-eye text-green-500 text-xs published-icon" title="Published"></i>' : '<i class="fas fa-eye-slash text-gray-500 text-xs published-icon" title="Unpublished"></i>'}
                        ${lecture.is_free_preview ? '<i class="fas fa-unlock text-blue-500 text-xs preview-icon" title="Free Preview"></i>' : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    addButton.insertAdjacentHTML('beforebegin', lectureHTML);

    // Re-initialize drag and drop for the chapter
    const chapterLecturesContainer = document.getElementById(`chapter-lectures-${chapterId}`);
    if (chapterLecturesContainer) {
        new Sortable(chapterLecturesContainer, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            filter: '.add-lecture-btn',
            onStart: function() {
                isDragging = true;
            },
            onEnd: function(evt) {
                isDragging = false;
                if (evt.oldIndex !== evt.newIndex) {
                    reorderLectures(chapterId);
                }
            }
        });
    }
}

function updateChapterLectureCount(chapterId) {
    const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);
    if (!chapterElement) return;

    const lectureCount = document.querySelectorAll(`#chapter-lectures-${chapterId} .lecture-item`).length;
    const countElement = chapterElement.querySelector('.text-xs.text-gray-400');
    if (countElement) {
        countElement.textContent = `${lectureCount} lecture${lectureCount !== 1 ? 's' : ''}`;
    }
}

// Expose functions to global scope
window.addChapter = addChapter;
window.addLecture = addLecture;
window.deleteChapter = deleteChapter;
window.deleteLecture = deleteLecture;
