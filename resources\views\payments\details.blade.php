@extends('layouts.app')

@section('title', 'Payment Details - Escape Matrix Academy')

@section('content')
<div class="py-12 bg-black min-h-screen">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('payments.history') }}" class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h1 class="text-3xl font-bold text-white">Payment Details</h1>
            </div>
            <p class="text-gray-400">Complete information about your payment</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Payment Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Payment Status Card -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-white">Payment Status</h2>
                        <span class="px-3 py-1 rounded-full text-sm font-medium
                            @if($payment->status === 'completed') bg-green-900 text-green-300
                            @elseif($payment->status === 'pending') bg-yellow-900 text-yellow-300
                            @elseif($payment->status === 'failed') bg-red-900 text-red-300
                            @elseif($payment->status === 'refunded') bg-blue-900 text-blue-300
                            @else bg-gray-900 text-gray-300
                            @endif">
                            {{ ucfirst($payment->status) }}
                        </span>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-400 mb-2">Payment Amount</h3>
                            <p class="text-2xl font-bold text-white">{{ $payment->formatted_amount }}</p>
                        </div>
                        
                        <div>
                            <h3 class="text-sm font-medium text-gray-400 mb-2">Payment Method</h3>
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.028-.026.056-.052.08-.65 3.85-3.197 5.341-6.957 5.341h-2.504c-.524 0-.968.382-1.05.9L8.937 19.9c-.013.06-.004.119.021.176.067.153.211.261.379.261h2.94c.458 0 .848-.334.922-.788l.04-.207.738-4.68.047-.257c.075-.453.465-.788.922-.788h.58c3.57 0 6.36-1.45 7.17-5.64.34-1.75.17-3.21-.72-4.25-.27-.31-.61-.56-1.01-.72z"/>
                                </svg>
                                <span class="text-white">{{ ucfirst($payment->payment_method ?? 'PayPal') }}</span>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-sm font-medium text-gray-400 mb-2">Transaction Date</h3>
                            <p class="text-white">{{ $payment->created_at->format('M j, Y g:i A') }}</p>
                        </div>

                        @if($payment->paid_at)
                        <div>
                            <h3 class="text-sm font-medium text-gray-400 mb-2">Payment Completed</h3>
                            <p class="text-white">{{ $payment->paid_at->format('M j, Y g:i A') }}</p>
                        </div>
                        @endif
                    </div>

                    @if($payment->payment_id)
                    <div class="mt-6 pt-6 border-t border-gray-700">
                        <h3 class="text-sm font-medium text-gray-400 mb-2">Transaction ID</h3>
                        <p class="text-white font-mono text-sm">{{ $payment->payment_id }}</p>
                    </div>
                    @endif
                </div>

                <!-- Course Information -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Course Details</h2>
                    
                    <div class="flex items-start space-x-4">
                        <img src="{{ $payment->course->image ? asset('storage/' . $payment->course->image) : 'https://via.placeholder.com/120x80' }}" 
                             alt="{{ $payment->course->title }}" 
                             class="w-20 h-14 object-cover rounded">
                        
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-white mb-2">{{ $payment->course->title }}</h3>
                            <p class="text-gray-400 text-sm mb-3">{{ Str::limit($payment->course->description, 150) }}</p>
                            
                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-400">
                                <span>{{ $payment->course->category }}</span>
                                <span>•</span>
                                <span>{{ ucfirst($payment->course->level) }}</span>
                                <span>•</span>
                                <span>{{ $payment->course->duration }}</span>
                            </div>
                        </div>
                    </div>

                    @if($payment->status === 'completed')
                    <div class="mt-6 pt-6 border-t border-gray-700">
                        <a href="{{ route('my-courses.view', $payment->course) }}" 
                           class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md transition-colors inline-flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <span>Access Course</span>
                        </a>
                    </div>
                    @endif
                </div>

                <!-- Payment Breakdown -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Payment Breakdown</h2>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Course Price</span>
                            <span class="text-white">${{ number_format($payment->amount, 2) }}</span>
                        </div>
                        
                        @if($payment->platform_fee > 0)
                        <!-- <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Platform Fee</span>
                            <span class="text-gray-500">${{ number_format($payment->platform_fee, 2) }}</span>
                        </div> -->
                        @endif
                        
                        <div class="border-t border-gray-700 pt-3">
                            <div class="flex justify-between font-semibold">
                                <span class="text-white">Total Paid</span>
                                <span class="text-white text-lg">${{ number_format($payment->amount, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Instructor Info -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Instructor</h3>
                    <div class="flex items-center space-x-3">
                        <img src="{{ $payment->course->instructor->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($payment->course->instructor->name) . '&background=4f46e5&color=fff' }}" 
                             alt="{{ $payment->course->instructor->name }}" 
                             class="w-10 h-10 rounded-full">
                        <div>
                            <h4 class="text-white font-medium">{{ $payment->course->instructor->name }}</h4>
                            <p class="text-gray-400 text-sm">Course Instructor</p>
                        </div>
                    </div>
                </div>

                <!-- Support -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Need Help?</h3>
                    <p class="text-gray-400 text-sm mb-4">
                        If you have any questions about this payment or need assistance, please contact our support team.
                    </p>
                    <a href="mailto:<EMAIL>" 
                       class="text-red-400 hover:text-red-300 text-sm transition-colors">
                        Contact Support
                    </a>
                </div>

                @if($payment->status === 'completed')
                <!-- Receipt -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Receipt</h3>
                    <p class="text-gray-400 text-sm mb-4">
                        A receipt for this payment has been sent to your email address.
                    </p>
                    <button onclick="window.print()" 
                            class="text-red-400 hover:text-red-300 text-sm transition-colors">
                        Print Receipt
                    </button>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
