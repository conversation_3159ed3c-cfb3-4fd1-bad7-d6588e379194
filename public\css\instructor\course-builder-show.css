/* Course Builder Show Page Styles */

/* Sortable.js drag and drop styles */
.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    transform: scale(1.02);
}

.sortable-drag {
    transform: rotate(5deg);
}

/* Lecture content visibility */
.lecture-content.hidden {
    display: none !important;
}

/* Chapter accordion styles */
.chapter-lectures {
    max-height: none;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, opacity 0.2s ease-in-out;
    opacity: 1;
}

.chapter-lectures.collapsed {
    max-height: 0 !important;
    opacity: 0;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

/* Chapter toggle icon transition */
.chapter-toggle {
    transition: transform 0.2s ease-in-out;
}

.chapter-item:hover .chapter-toggle {
    transform: scale(1.1);
}

/* Auto-save indicator */
.auto-save-indicator {
    transition: all 0.3s ease;
}

/* Selection highlighting */
.chapter-item.bg-red-900 {
    background-color: rgba(127, 29, 29, 0.5) !important;
}

.lecture-item.bg-red-900 {
    background-color: rgba(127, 29, 29, 0.3) !important;
}

/* Sidebar transitions */
#curriculum-sidebar {
    transition: transform 0.3s ease-in-out;
}

/* Mobile overlay styling */
#mobile-sidebar-overlay {
    backdrop-filter: blur(2px);
}

/* Touch feedback */
.touch-feedback {
    background-color: rgba(127, 29, 29, 0.2);
    transition: background-color 0.15s ease;
}

/* Prevent body scroll when mobile sidebar is open */
body.overflow-hidden {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* Mobile/Tablet-specific styles */
@media (max-width: 1023px) {
    /* Ensure mobile sidebar is properly positioned */
    #curriculum-sidebar {
        top: 0;
        bottom: 0;
        height: 100vh;
        max-height: 100vh;
    }

    /* Mobile touch targets */
    .chapter-item, .lecture-item {
        min-height: 48px; /* Minimum touch target size */
    }

    /* Mobile button sizing */
    button {
        min-height: 44px; /* iOS recommended touch target */
        min-width: 44px;
    }

    /* Mobile form inputs */
    input, textarea, select {
        font-size: 16px; /* Prevent zoom on iOS */
        min-height: 44px;
    }

    /* Mobile spacing adjustments */
    .chapter-header {
        padding: 1rem !important;
    }

    .lecture-item {
        padding: 0.75rem !important;
    }

    /* Mobile text sizing */
    .chapter-item .text-white {
        font-size: 0.95rem;
    }

    .lecture-item .text-gray-300 {
        font-size: 0.875rem;
    }

    /* Hide drag handles on mobile for cleaner look */
    .drag-handle {
        display: none;
    }

    /* Mobile-friendly scrolling */
    .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
    }
}

/* Note: Tablets now use the same mobile layout (sidebar hidden) for better content space */

/* Enhanced File Upload Styles */
.file-upload-zone {
    position: relative;
}

.upload-area {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.upload-area.drag-over {
    border-color: #dc2626 !important;
    background-color: rgba(220, 38, 38, 0.1) !important;
    transform: scale(1.02);
}

.upload-icon {
    transition: all 0.3s ease;
}

.upload-area:hover .upload-icon i {
    transform: scale(1.1);
    color: #dc2626;
}

.uploaded-files-container {
    max-height: 400px;
    overflow-y: auto;
}

.file-item {
    transition: all 0.2s ease;
    position: relative;
}

.file-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(55, 65, 81, 0.5);
    border-radius: 8px;
    flex-shrink: 0;
}

.file-info {
    min-width: 0;
}

.file-item button {
    opacity: 0.7;
    transition: all 0.2s ease;
}

.file-item:hover button {
    opacity: 1;
}

.file-item button:hover {
    transform: scale(1.1);
}

.upload-progress-container {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.no-files-message {
    font-style: italic;
    border: 2px dashed #374151;
    border-radius: 8px;
    background: rgba(55, 65, 81, 0.2);
}

/* File type specific colors */
.file-item .fa-file-pdf { color: #ef4444; }
.file-item .fa-file-word { color: #3b82f6; }
.file-item .fa-file-powerpoint { color: #f97316; }
.file-item .fa-file-excel { color: #10b981; }
.file-item .fa-file-archive { color: #eab308; }
.file-item .fa-file-image { color: #8b5cf6; }
.file-item .fa-file-alt { color: #6b7280; }
.file-item .fa-file-csv { color: #059669; }

/* Responsive file upload */
@media (max-width: 768px) {
    .upload-area {
        padding: 1.5rem !important;
    }

    .upload-icon {
        margin-bottom: 0.75rem !important;
    }

    .upload-icon i {
        font-size: 2.5rem !important;
    }

    .upload-text p:first-child {
        font-size: 1rem !important;
        margin-bottom: 0.5rem !important;
    }

    .upload-text p:last-child {
        font-size: 0.75rem !important;
    }

    .file-item {
        padding: 0.75rem !important;
    }

    .file-icon {
        width: 32px;
        height: 32px;
    }

    .file-icon i {
        font-size: 1.25rem !important;
    }

    .file-item button {
        padding: 0.5rem !important;
        min-width: 36px;
        min-height: 36px;
    }
}

/* Loading and Animation Enhancements */
.upload-area.uploading {
    pointer-events: none;
    opacity: 0.7;
    position: relative;
}

.upload-area.uploading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 2px solid #dc2626;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.file-item.uploading {
    opacity: 0.6;
    position: relative;
}

.file-item.uploading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 1rem;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    border: 2px solid #dc2626;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Success animations */
.file-item.upload-success {
    animation: successPulse 0.6s ease-out;
    border-color: #10b981 !important;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

/* Error states */
.file-item.upload-error {
    border-color: #ef4444 !important;
    background-color: rgba(239, 68, 68, 0.1) !important;
}

.upload-area.upload-error {
    border-color: #ef4444 !important;
    background-color: rgba(239, 68, 68, 0.1) !important;
}

/* Modal animations */
.modal-backdrop {
    animation: fadeIn 0.2s ease-out;
}

.modal-content {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Professional button styles */
.btn-primary {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    box-shadow: 0 4px 14px 0 rgba(220, 38, 38, 0.3);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    box-shadow: 0 6px 20px 0 rgba(220, 38, 38, 0.4);
    transform: translateY(-1px);
}

.btn-secondary {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    box-shadow: 0 4px 14px 0 rgba(75, 85, 99, 0.3);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    box-shadow: 0 6px 20px 0 rgba(75, 85, 99, 0.4);
    transform: translateY(-1px);
}

/* Tooltip styles */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}
